# 部署指南

## 🎯 项目概述

本项目已完成环境配置优化，支持开发、测试、生产三种环境的独立构建和部署。

## 📁 构建输出

不同环境的构建文件输出到独立目录：

```
dist/
├── development/     # 开发环境构建
├── test/           # 测试环境构建
└── production/     # 生产环境构建
```

## 🚀 快速部署

### 测试环境部署

```bash
# 1. 构建测试版本
npm run build:test

# 2. 部署 dist/test/ 目录到测试服务器
# 例如：rsync -av dist/test/ user@test-server:/var/www/html/
```

### 生产环境部署

```bash
# 1. 清理并构建生产版本
npm run clean:build

# 2. 部署 dist/production/ 目录到生产服务器
# 例如：rsync -av dist/production/ user@prod-server:/var/www/html/
```

## 🔧 环境配置验证

部署前请运行环境配置检查：

```bash
npm run test:env
```

确保所有环境变量配置正确。

## 📊 构建特性对比

| 特性 | 开发环境 | 测试环境 | 生产环境 |
|------|----------|----------|----------|
| Source Map | ✅ | ✅ | ❌ |
| 代码压缩 | ❌ | ❌ | ✅ |
| 控制台日志 | ✅ | ❌ | ❌ |
| 开发者工具 | ✅ | ❌ | ❌ |
| 文件名 Hash | ❌ | ❌ | ✅ |
| API 地址 | dev.nustaronline.vip | test.nustaronline.vip | io.nustargame.com |

## 🌐 API 地址配置

- **开发环境**: `https://dev.nustaronline.vip`
- **测试环境**: `https://test.nustaronline.vip`
- **生产环境**: `https://io.nustargame.com`

## 🛠️ 可用脚本

```bash
# 开发
npm run dev              # 启动开发服务器（开发环境）
npm run dev:test         # 启动开发服务器（测试环境）

# 构建
npm run build:dev        # 构建开发版本
npm run build:test       # 构建测试版本
npm run build:prod       # 构建生产版本

# 预览
npm run preview          # 预览构建结果
npm run preview:test     # 预览测试版本
npm run preview:prod     # 预览生产版本

# 工具
npm run clean            # 清理构建目录
npm run clean:build      # 清理并构建生产版本
npm run test:env         # 检查环境配置
npm run type-check       # TypeScript 类型检查
npm run lint             # 代码检查
npm run format           # 代码格式化
```

## 🔍 问题排查

### 1. 构建失败

```bash
# 检查 Node.js 版本（推荐 18+）
node --version

# 清理依赖重新安装
rm -rf node_modules package-lock.json
npm install

# 检查环境配置
npm run test:env
```

### 2. API 请求 304 错误

项目已实现缓存控制机制：
- 自动添加时间戳参数
- 设置缓存控制头
- 智能重试机制

### 3. 环境变量不生效

确保环境变量文件存在且格式正确：
- `.env.development`
- `.env.test`
- `.env.production`

## 📝 注意事项

1. **环境隔离**: 不同环境使用独立的 API 地址和配置
2. **缓存处理**: 已解决 304 缓存问题，无需手动清理
3. **类型安全**: 完整的 TypeScript 类型定义
4. **向后兼容**: 现有代码无需修改

## 🎉 部署成功验证

部署完成后，请验证：

1. ✅ 页面正常加载
2. ✅ API 请求地址正确
3. ✅ 支付状态查询功能正常
4. ✅ 控制台无错误信息（生产环境应无日志输出）

## 📞 技术支持

如遇问题，请检查：
1. 构建日志输出
2. 浏览器控制台错误
3. 网络请求状态
4. 环境配置是否正确
