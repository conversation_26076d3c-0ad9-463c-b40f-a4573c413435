# 构建配置文档

## 环境配置

项目支持三种环境：开发环境（development）、测试环境（test）、生产环境（production）

### 环境变量文件

- `.env` - 默认环境变量
- `.env.development` - 开发环境变量
- `.env.test` - 测试环境变量
- `.env.production` - 生产环境变量

### 环境变量说明

| 变量名                 | 说明               | 示例值                          |
| ---------------------- | ------------------ | ------------------------------- |
| `VITE_APP_TITLE`       | 应用标题           | `Nustar Pay Result (Test)`      |
| `VITE_APP_ENV`         | 应用环境           | `development/test/production`   |
| `VITE_BASE_URL`        | API 基础地址       | `https://test.nustaronline.vip` |
| `VITE_API_TIMEOUT`     | API 超时时间(ms)   | `30000`                         |
| `VITE_ENABLE_MOCK`     | 是否启用 Mock      | `false`                         |
| `VITE_ENABLE_DEVTOOLS` | 是否启用开发者工具 | `true`                          |
| `VITE_ENABLE_CONSOLE`  | 是否启用控制台日志 | `true`                          |
| `VITE_PROXY_TARGET`    | 代理目标地址       | `https://test.nustaronline.vip` |

## 构建脚本

### 开发环境

```bash
# 启动开发服务器（开发环境）
npm run dev

# 启动开发服务器（测试环境）
npm run dev:test

# 构建开发版本
npm run build:dev
```

### 测试环境

```bash
# 构建测试版本
npm run build:test

# 预览测试版本
npm run preview:test
```

### 生产环境

```bash
# 构建生产版本
npm run build:prod

# 预览生产版本
npm run preview:prod

# 清理并构建生产版本
npm run clean:build
```

### 其他脚本

```bash
# 类型检查
npm run type-check

# 代码格式化
npm run format

# 代码检查
npm run lint

# 清理构建目录
npm run clean
```

## 构建输出

不同环境的构建文件会输出到不同目录：

- 开发环境：`dist/development/`
- 测试环境：`dist/test/`
- 生产环境：`dist/production/`

## 环境特性

### 开发环境 (development)

- ✅ 启用 Source Map
- ✅ 启用控制台日志
- ✅ 启用开发者工具
- ✅ 不压缩代码
- ✅ 使用代理服务器

### 测试环境 (test)

- ✅ 启用 Source Map
- ❌ 禁用控制台日志
- ❌ 禁用开发者工具
- ❌ 不压缩代码
- ✅ 直接连接测试服务器

### 生产环境 (production)

- ❌ 禁用 Source Map
- ❌ 禁用控制台日志
- ❌ 禁用开发者工具
- ✅ 压缩代码
- ✅ 文件名包含 hash
- ✅ 直接连接生产服务器

## 代理配置

本地开发时，所有 `/open` 开头的请求会被代理到对应环境的服务器：

- 开发环境：`https://dev.nustaronline.vip`
- 测试环境：`https://test.nustaronline.vip`
- 生产环境：`https://io.nustargame.com`

## 缓存处理

为了解决 304 缓存问题，项目实现了以下机制：

1. **全局缓存控制头**：

   - `Cache-Control: no-cache, no-store, must-revalidate`
   - `Pragma: no-cache`
   - `Expires: 0`

2. **GET 请求时间戳**：

   - 自动为所有 GET 请求添加 `_t` 时间戳参数

3. **环境标识头**：
   - 添加 `X-App-Env` 请求头标识当前环境

## 使用示例

### 在代码中使用环境变量

```typescript
import { envConfig, printEnvInfo } from "@/utils/env";

// 打印环境信息
printEnvInfo();

// 判断环境
if (envConfig.isDev) {
  console.log("开发环境");
}

// 获取配置
const apiUrl = envConfig.baseURL;
const timeout = envConfig.timeout;
```

### 条件编译

```typescript
// 只在开发环境执行
if (import.meta.env.VITE_APP_ENV === "development") {
  // 开发环境代码
}

// 使用环境变量
const apiUrl = import.meta.env.VITE_BASE_URL;
```

## 部署建议

1. **测试环境部署**：

   ```bash
   npm run build:test
   # 部署 dist/test/ 目录
   ```

2. **生产环境部署**：

   ```bash
   npm run clean:build
   # 部署 dist/production/ 目录
   ```

3. **环境验证**：
   - 检查控制台是否输出环境信息
   - 验证 API 请求地址是否正确
   - 确认功能在目标环境正常工作
