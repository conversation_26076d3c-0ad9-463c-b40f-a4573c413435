#!/usr/bin/env node

/**
 * 环境配置测试脚本
 * 用于验证不同环境的配置是否正确
 */

const fs = require("fs");
const path = require("path");

// 环境配置文件列表
const envFiles = [".env", ".env.development", ".env.test", ".env.staging", ".env.production"];

// 必需的环境变量
const requiredVars = ["VITE_APP_TITLE", "VITE_APP_ENV", "VITE_BASE_URL", "VITE_API_TIMEOUT", "VITE_PROXY_TARGET", "VITE_JUMP_URL"];

console.log("🔍 检查环境配置文件...\n");

envFiles.forEach((file) => {
  const filePath = path.join(process.cwd(), file);

  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${file} - 文件不存在`);
    return;
  }

  console.log(`✅ ${file} - 文件存在`);

  // 读取并解析环境变量
  const content = fs.readFileSync(filePath, "utf8");
  const vars = {};

  content.split("\n").forEach((line) => {
    line = line.trim();
    if (line && !line.startsWith("#")) {
      const [key, value] = line.split("=");
      if (key && value) {
        vars[key.trim()] = value.trim();
      }
    }
  });

  // 检查必需变量
  const missing = requiredVars.filter((varName) => !vars[varName]);
  if (missing.length > 0) {
    console.log(`   ⚠️  缺少变量: ${missing.join(", ")}`);
  } else {
    console.log(`   ✅ 所有必需变量都存在`);
  }

  // 显示关键配置
  console.log(`   📝 环境: ${vars.VITE_APP_ENV || "undefined"}`);
  console.log(`   📝 标题: ${vars.VITE_APP_TITLE || "undefined"}`);
  console.log(`   📝 API地址: ${vars.VITE_BASE_URL || "undefined"}`);
  console.log(`   📝 跳转地址: ${vars.VITE_JUMP_URL || "undefined"}`);
  console.log("");
});

console.log("🎯 环境配置检查完成！");
console.log("\n📚 使用说明:");
console.log("  开发环境: npm run dev");
console.log("  测试环境: npm run dev:test");
console.log("  预发环境: npm run dev:staging");
console.log("  构建测试: npm run build:test");
console.log("  构建预发: npm run build:staging");
console.log("  构建生产: npm run build:prod");
