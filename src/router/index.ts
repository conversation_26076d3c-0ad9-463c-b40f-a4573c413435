import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: "home",
      path: "/",
      component: () => import("../pages/PayResult/index.vue"),
    },
    {
      name: "payResult",
      path: "/payResult",
      component: () => import("../pages/PayResult/index.vue"),
    },
    {
      name: "download",
      path: "/download",
      component: () => import("../pages/DownloadPage/index.vue"),
    },
    {
      name: "notSupported",
      path: "/notSupported",
      component: () => import("../pages/DownloadPage/notSupported.vue"),
    },
  ],
});

export default router;
