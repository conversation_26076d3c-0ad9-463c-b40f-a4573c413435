<script setup lang="ts" name="NotSupported">
import { useRouter } from "vue-router";

const router = useRouter();

// 返回上一页
function goBack() {
  router.go(-1);
}

// 访问网站
function visitWebsite() {
  // 这里可以配置实际的网站URL
  window.open("https://www.nustar.ph", "_blank");
}
</script>

<template>
  <div class="not-supported-page">
    <!-- 手机框架 -->
    <div class="phone-frame">
      <!-- 顶部状态栏区域 -->
      <div class="status-bar"></div>

      <!-- 主要内容区域 -->
      <div class="content-area">
        <!-- 主标题 -->
        <h1 class="main-title">Oops!</h1>

        <!-- 提示信息 -->
        <p class="message">Sorry, our app aren't available in IOS yet.</p>

        <!-- 访问说明 -->
        <p class="instruction">Visit Nustar by clicking below URL.</p>

        <!-- 网站链接 -->
        <a href="#" @click.prevent="visitWebsite" class="website-link"> www.nustar.ph </a>
      </div>

      <!-- 底部指示器 -->
      <div class="home-indicator"></div>
    </div>

    <!-- 返回按钮 -->
    <button @click="goBack" class="back-button">← Back</button>
  </div>
</template>

<style lang="scss" scoped>
.not-supported-page {
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

.phone-frame {
  width: 375px;
  height: 812px;
  background: #f8f9fa;
  border-radius: 40px;
  border: 8px solid #e9ecef;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.status-bar {
  height: 44px;
  background: #f8f9fa;
  border-radius: 32px 32px 0 0;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
    width: 134px;
    height: 5px;
    background: #000;
    border-radius: 2.5px;
    opacity: 0.8;
  }
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 30px;
  text-align: center;
}

.main-title {
  font-size: 48px;
  font-weight: 700;
  color: #333;
  margin: 0 0 40px 0;
  letter-spacing: -0.02em;
}

.message {
  font-size: 18px;
  font-weight: 500;
  color: #555;
  margin: 0 0 30px 0;
  line-height: 1.4;
  max-width: 280px;
}

.instruction {
  font-size: 16px;
  font-weight: 400;
  color: #666;
  margin: 0 0 20px 0;
  line-height: 1.4;
}

.website-link {
  font-size: 18px;
  font-weight: 600;
  color: #007aff;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: #0056cc;
  }

  &:active {
    color: #004499;
  }
}

.home-indicator {
  height: 34px;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: "";
    width: 134px;
    height: 5px;
    background: #000;
    border-radius: 2.5px;
    opacity: 0.3;
  }
}

.back-button {
  position: absolute;
  top: 30px;
  left: 30px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  border-radius: 12px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-1px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 480px) {
  .not-supported-page {
    padding: 10px;
  }

  .phone-frame {
    width: 100%;
    max-width: 375px;
    height: 100vh;
    max-height: 812px;
    border-radius: 20px;
    border-width: 4px;
  }

  .back-button {
    top: 20px;
    left: 20px;
    padding: 10px 16px;
    font-size: 14px;
  }

  .main-title {
    font-size: 40px;
  }

  .message {
    font-size: 16px;
  }

  .instruction {
    font-size: 14px;
  }

  .website-link {
    font-size: 16px;
  }
}
</style>
