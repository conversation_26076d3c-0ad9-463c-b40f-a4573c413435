<script setup lang="ts" name="Landing">
import { ref, onMounted, computed, onBeforeMount } from "vue";
import { getPayStatus } from "@/service";
import { parseQuery } from "@/utils";
import { envConfig } from "@/utils/env";
import Loading from "@/components/Loading.vue";

const PAGE_CONTENT = {
  1: {
    buttonText: "Go Play",
    title: "Success",
    img: "/src/assets/img/success.png",
    imgStyle: { width: "266px", height: "auto" },
    titleColor: "#01D46A",
    urlParam: "type=pay&id=success",
    extraContent: `You have successfully made a deposit.\n Good luck!`,
  },
  2: {
    buttonText: "Back to Nustar",
    title: "Unsuccessful",
    img: "/src/assets/img/unSuccessful.png",
    titleColor: "#FF5353",
    urlParam: "type=pay&id=failed",
    extraContent: "Your deposit could not be completed due to a system error. Please try again later.",
  },
  3: {
    buttonText: "Back to Nustar",
    title: "Pending",
    img: "/src/assets/img/pending.png",
    titleColor: "#5690FF",
    urlParam: "type=pay&id=pending",
    extraContent: `Your deposit request is currently being processed.\n Please kindly wait.`,
  },
  4: {
    buttonText: "Back to Nustar",
    title: "Canceled",
    img: "/src/assets/img/unSuccessful.png",
    titleColor: "#FF5353",
    urlParam: "type=pay&id=canceled",
    extraContent: "Your deposit could not be completed due to a system error. Please try again later.",
  },
};

const query = parseQuery(location.search);

const payStatus = ref<{
  status: number; // 1 成功 其他 失败
  content: string; // 提示信息
  urlParam: string;
  amount: string | number;
}>();
const terminal = ref(Number(query.terminal as string));

const loading = ref(false);
async function init() {
  loading.value = true;
  const orderId = query.order_id as string;
  const userId = query.user_id as string;
  const sign = query.sign as string;
  const timestamp = query.timestamp as string;
  if (!orderId && !userId && !sign && !timestamp) {
    loading.value = false;
    return;
  }
  try {
    const res = await getPayStatus({ order_id: orderId, user_id: userId, sign, timestamp });
    payStatus.value = { ...res.data, ...PAGE_CONTENT[res.data.status] };
  } catch (error) {
  } finally {
    loading.value = false;
  }
}
//关闭页面
function closePage() {
  if (navigator.userAgent.indexOf("Firefox") != -1 || navigator.userAgent.indexOf("Chrome") != -1) {
    window.location.href = "about:blank";
    window.close();
  } else {
    window.opener = null;
    window.open("", "_self");
    window.close();
  }
}

// 充值失败跳转GCash-h5充值页面；充值成功跳转游戏history页面
function handleClick() {
  const hasParams = envConfig.jumpURL.indexOf("?") > -1;
  const newUrl = envConfig.jumpURL + (hasParams ? "&" : "/?") + payStatus.value?.urlParam + "&amount=" + payStatus.value?.amount;
  window.location.href = newUrl;
}

// onBeforeMount(() => {
init();
// });
</script>

<template>
  <Loading v-if="loading" />
  <div class="container" v-else-if="payStatus?.status">
    <div class="content">
      <img class="icon" :src="payStatus?.img" :style="payStatus?.imgStyle || {}" />
      <span :style="{ color: payStatus?.titleColor || '#5690FF' }">{{ payStatus?.title }}</span>
      <div class="bottom">
        {{ payStatus?.content || payStatus?.extraContent }}
      </div>
    </div>

    <div v-if="payStatus?.buttonText" class="button" @click="handleClick">
      {{ payStatus?.buttonText }}
    </div>
  </div>
  <div class="container" v-else>
    <div class="content">
      <img class="icon" src="/src/assets/img/unSuccessful.png" />
      <span>Failed</span>
      <div class="bottom">Error Request</div>
    </div>
    <div class="button" @click="handleClick">Back to Nustar</div>
  </div>
</template>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  height: 100vh;
  height: 100dvh;

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 700;
    line-height: 48px;
    color: #ff5353;

    .icon {
      margin-bottom: 30px;
      width: 148px;
      height: auto;
    }
  }

  .bottom {
    white-space: pre-line;
    padding: 0 20px 60px;
    width: 100%;
    color: #8d949d;
    font-size: 14px;
    font-family: "Inter";
    font-weight: 400;
    line-height: 28px;
    letter-spacing: 0%;
    white-space: pre-line;
  }

  .button {
    width: 80%;
    margin: 30px auto;
    background-color: #2b89f6;
    color: #fff;
    height: 48px;
    border-radius: 48px;
    line-height: 48px;
    font-size: 16px;
    font-weight: 800;
    color: #fff;
    background-color: #ac1140;
  }

  .demo-link {
    text-align: center;
    margin: 20px auto;
    padding: 10px;

    a {
      display: inline-block;
      padding: 8px 16px;
      border: 1px solid #2b89f6;
      border-radius: 20px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #2b89f6;
        color: white !important;
        transform: translateY(-1px);
      }
    }
  }
}
</style>
