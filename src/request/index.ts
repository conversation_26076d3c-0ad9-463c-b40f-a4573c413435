import axios from "axios";
import type { InternalAxiosRequestConfig } from "axios";
import { getQuery, filterData, signEncrypt } from "../utils";

const isDev = window.location.hostname === "localhost" || window.location.href.indexOf("192.168.") > -1 || window.location.hostname === "127.0.0.1";

// 根据环境确定 baseURL
const getBaseURL = () => {
  return import.meta.env.VITE_BASE_URL || "https://dev.nustaronline.vip";
};

const baseURL = isDev ? "/local" : getBaseURL();

const instance = axios.create({
  baseURL,
  timeout: Number(import.meta.env.VITE_API_TIMEOUT) || 30000,
  headers: {
    "Cache-Control": "no-cache, no-store, must-revalidate",
    Pragma: "no-cache",
    Expires: "0",
  },
});
// 添加请求拦截器
instance.interceptors.request.use(
  function (config: InternalAxiosRequestConfig) {
    const query = getQuery(window.location.search);
    const data = filterData(config.data || config.params || {});

    // 验签参数配置
    const terminal: number = (data.terminal as number) || (query.terminal ? Number(query.terminal) : 128);
    config.headers["terminal"] = terminal;

    // 环境相关的请求头
    if (import.meta.env.VITE_APP_ENV) {
      config.headers["X-App-Env"] = import.meta.env.VITE_APP_ENV;
    }

    return config;
  },
  function (error: any) {
    console.error("Request interceptor error:", error);
    return Promise.reject(error);
  }
);

// 添加响应拦截器
instance.interceptors.response.use(
  function (response: any) {
    // 2xx 范围内的状态码都会触发该函数。
    // 对响应数据做点什么

    // 在开发环境下打印响应信息
    if (import.meta.env.VITE_ENABLE_CONSOLE === "true") {
    }

    return response;
  },
  function (error: any) {
    // 超出 2xx 范围的状态码都会触发该函数。
    // 对响应错误做点什么
    console.error("Response error:", error);

    // 根据不同的错误状态码进行处理
    if (error.response) {
      const { status, data } = error.response;
      switch (status) {
        case 304:
          console.warn("Resource not modified (304), consider cache handling");
          break;
        case 401:
          console.error("Unauthorized access");
          break;
        case 403:
          console.error("Forbidden access");
          break;
        case 404:
          console.error("Resource not found");
          break;
        case 500:
          console.error("Internal server error");
          break;
        default:
          console.error(`HTTP Error ${status}:`, data);
      }
    }

    return Promise.reject(error);
  }
);

export default instance;
